'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Activity,
  BarChart3,
  ShoppingBag,
  TrendingDown,
  TrendingUp,
  Users,
  DollarSign,
  Package,
  Receipt,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { useBusinessPurchaseAnalytics } from '@/hooks/use-business-purchase-analytics'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

interface BusinessPurchaseAnalyticsProps {
  className?: string
}


function BusinessPurchaseAnalytics({ className }: BusinessPurchaseAnalyticsProps) {
  const { data: analytics, isLoading, error } = useBusinessPurchaseAnalytics()
  const [expandedItems, setExpandedItems] = useState(false)

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Business Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Skeleton className="h-20" />
            <Skeleton className="h-20" />
            <Skeleton className="h-20" />
            <Skeleton className="h-20" />
          </div>
          <Skeleton className="h-64" />
          <Skeleton className="h-48" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Business Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Unable to load business analytics</p>
            <p className="text-sm">{error instanceof Error ? error.message : 'Unknown error'}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!analytics) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Business Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No business data available</p>
            <p>Analytics will appear once you have sales data</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const topItemsByQuantity = analytics.top_items.by_quantity.slice(0, expandedItems ? 10 : 5)
  const topSpendingMembers = analytics.member_analytics?.top_spending_members || []
  const weeklyItemTrends = analytics.weekly_item_trends || []
  const weeklySpenderTrends = analytics.weekly_spender_trends || []

  // Prepare chart data (keeping for potential future use)
  // const monthlyTrendsData = analytics.monthly_trends.slice(-6) // Last 6 months

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBag className="h-5 w-5" />
          Business Purchase Analytics
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Comprehensive insights into your business performance and sales trends
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-600">Total Revenue</span>
            </div>
            <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
              ETB {analytics.summary.total_revenue.toLocaleString()}
            </div>
            {analytics.growth.revenue_growth !== 0 && (
              <div className={`flex items-center gap-1 text-xs ${
                analytics.growth.revenue_growth > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {analytics.growth.revenue_growth > 0 ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                {Math.abs(analytics.growth.revenue_growth).toFixed(1)}% vs last month
              </div>
            )}
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Receipt className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-600">Total Orders</span>
            </div>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              {analytics.summary.total_receipts.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">
              {analytics.recent_activity.receipts} in last 30 days
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-600">Avg Order Value</span>
            </div>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              ETB {Math.round(analytics.summary.avg_order_value).toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">
              ETB {Math.round(analytics.recent_activity.avg_order_value)} recent avg
            </div>
          </div>

          <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-600">Active Members</span>
            </div>
            <div className="text-2xl font-bold text-amber-700 dark:text-amber-300">
              {analytics.summary.total_members.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">
              {analytics.recent_activity.active_members} active in 30 days
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <Tabs defaultValue="members" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="members" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Members
            </TabsTrigger>
            <TabsTrigger value="items" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Items
            </TabsTrigger>
            <TabsTrigger value="item-trends" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Item Trends
            </TabsTrigger>
            <TabsTrigger value="spender-trends" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Top Spenders
            </TabsTrigger>
          </TabsList>

          <TabsContent value="item-trends" className="space-y-4">
            <div>
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Top 5 Popular Items by Week (Last 6 Weeks)
              </h4>
              {weeklyItemTrends.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={weeklyItemTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="week" />
                      <YAxis />
                      <Tooltip
                        formatter={(value: number, name: string, props: { payload?: Record<string, unknown> }) => {
                          const payload = props.payload;
                          let itemName = 'Unknown Item';

                          // Match the name format "#1 Item", "#2 Item", etc.
                          if (name === '#1 Item' && payload?.item1_name) {
                            itemName = payload.item1_name as string;
                          } else if (name === '#2 Item' && payload?.item2_name) {
                            itemName = payload.item2_name as string;
                          } else if (name === '#3 Item' && payload?.item3_name) {
                            itemName = payload.item3_name as string;
                          } else if (name === '#4 Item' && payload?.item4_name) {
                            itemName = payload.item4_name as string;
                          } else if (name === '#5 Item' && payload?.item5_name) {
                            itemName = payload.item5_name as string;
                          }

                          // If we still have "Unknown Item", use positional fallbacks
                          if (itemName === 'Unknown Item') {
                            if (name === '#1 Item') itemName = 'Top Item';
                            else if (name === '#2 Item') itemName = '2nd Most Popular';
                            else if (name === '#3 Item') itemName = '3rd Most Popular';
                            else if (name === '#4 Item') itemName = '4th Most Popular';
                            else if (name === '#5 Item') itemName = '5th Most Popular';
                          }

                          return [`${value} orders`, itemName];
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="item1"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ fill: '#8884d8' }}
                        name="#1 Item"
                      />
                      <Line
                        type="monotone"
                        dataKey="item2"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        dot={{ fill: '#82ca9d' }}
                        name="#2 Item"
                      />
                      <Line
                        type="monotone"
                        dataKey="item3"
                        stroke="#ffc658"
                        strokeWidth={2}
                        dot={{ fill: '#ffc658' }}
                        name="#3 Item"
                      />
                      <Line
                        type="monotone"
                        dataKey="item4"
                        stroke="#ff7300"
                        strokeWidth={2}
                        dot={{ fill: '#ff7300' }}
                        name="#4 Item"
                      />
                      <Line
                        type="monotone"
                        dataKey="item5"
                        stroke="#8dd1e1"
                        strokeWidth={2}
                        dot={{ fill: '#8dd1e1' }}
                        name="#5 Item"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No weekly trend data available</p>
                    <p className="text-sm">Data will appear as transactions are processed</p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="spender-trends" className="space-y-4">
            <div>
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Top 5 Spenders by Week (Last 6 Weeks)
              </h4>
              {weeklySpenderTrends.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={weeklySpenderTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="week" />
                      <YAxis />
                      <Tooltip
                        formatter={(value: number, name: string, props: { payload?: Record<string, unknown> }) => {
                          const payload = props.payload;

                          let spenderName = 'Unknown Spender';

                          // Match the name format "#1 Spender", "#2 Spender", etc.
                          if (name === '#1 Spender' && payload?.spender1_name) {
                            spenderName = payload.spender1_name as string;
                          } else if (name === '#2 Spender' && payload?.spender2_name) {
                            spenderName = payload.spender2_name as string;
                          } else if (name === '#3 Spender' && payload?.spender3_name) {
                            spenderName = payload.spender3_name as string;
                          } else if (name === '#4 Spender' && payload?.spender4_name) {
                            spenderName = payload.spender4_name as string;
                          } else if (name === '#5 Spender' && payload?.spender5_name) {
                            spenderName = payload.spender5_name as string;
                          }

                          // If we still have "Unknown Spender", use positional fallbacks
                          if (spenderName === 'Unknown Spender') {
                            if (name === '#1 Spender') spenderName = 'Top Spender';
                            else if (name === '#2 Spender') spenderName = '2nd Top Spender';
                            else if (name === '#3 Spender') spenderName = '3rd Top Spender';
                            else if (name === '#4 Spender') spenderName = '4th Top Spender';
                            else if (name === '#5 Spender') spenderName = '5th Top Spender';
                          }

                          return [`ETB ${value.toLocaleString()}`, spenderName];
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="spender1"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ fill: '#8884d8' }}
                        name="#1 Spender"
                      />
                      <Line
                        type="monotone"
                        dataKey="spender2"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        dot={{ fill: '#82ca9d' }}
                        name="#2 Spender"
                      />
                      <Line
                        type="monotone"
                        dataKey="spender3"
                        stroke="#ffc658"
                        strokeWidth={2}
                        dot={{ fill: '#ffc658' }}
                        name="#3 Spender"
                      />
                      <Line
                        type="monotone"
                        dataKey="spender4"
                        stroke="#ff7300"
                        strokeWidth={2}
                        dot={{ fill: '#ff7300' }}
                        name="#4 Spender"
                      />
                      <Line
                        type="monotone"
                        dataKey="spender5"
                        stroke="#8dd1e1"
                        strokeWidth={2}
                        dot={{ fill: '#8dd1e1' }}
                        name="#5 Spender"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No weekly spender data available</p>
                    <p className="text-sm">Data will appear as transactions are processed</p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="members" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              <div className="lg:col-span-2 xl:col-span-2">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Top Spending Members
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {topSpendingMembers.length > 0 ? (
                    topSpendingMembers.slice(0, 8).map((member: { member_id: string; name: string; phone_number: string; total_spent: number; transaction_count: number; avg_order_value: number; favorite_item?: string | null }, index: number) => (
                      <div key={member.member_id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                            <span className="text-sm font-bold text-primary">#{index + 1}</span>
                          </div>
                          <div>
                            <div className="font-medium">{member.name || 'Anonymous'}</div>
                            <div className="text-sm text-muted-foreground">
                              {member.phone_number}
                            </div>
                            {member.favorite_item && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Favorite: {member.favorite_item}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-green-600">
                            ETB {member.total_spent?.toLocaleString() || '0'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {member.transaction_count} orders
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ETB {Math.round(member.avg_order_value || 0)} avg
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground col-span-2">
                      <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No member data available</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Summary Stats Sidebar */}
              <div className="xl:col-span-1">
                <h4 className="font-semibold mb-3">Member Insights</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm text-blue-600 font-medium">Total Active Members</div>
                    <div className="text-2xl font-bold text-blue-700">
                      {analytics.recent_activity?.active_members || analytics.summary?.total_members || 0}
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-green-600 font-medium">Avg Order Value</div>
                    <div className="text-2xl font-bold text-green-700">
                      ETB {Math.round(analytics.summary?.avg_order_value || 0)}
                    </div>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <div className="text-sm text-purple-600 font-medium">Top Member Spent</div>
                    <div className="text-2xl font-bold text-purple-700">
                      ETB {topSpendingMembers[0]?.total_spent?.toLocaleString() || '0'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="items" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Top Selling Items
                </h4>
                <div className="space-y-3">
                  {topItemsByQuantity.map((item, index) => (
                    <div key={item.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                          <span className="text-sm font-bold text-primary">#{index + 1}</span>
                        </div>
                        <div>
                          <div className="font-medium">{item.name}</div>
                          {item.category && (
                            <Badge variant="outline" className="text-xs">
                              {item.category}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{item.quantity_sold} sold</div>
                        <div className="text-sm text-muted-foreground">
                          ETB {item.revenue.toLocaleString()} revenue
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {item.orders} orders
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {analytics.top_items.by_quantity.length > 5 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full mt-3"
                    onClick={() => setExpandedItems(!expandedItems)}
                  >
                    {expandedItems ? (
                      <>
                        <ChevronUp className="h-4 w-4 mr-2" />
                        Show Less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-4 w-4 mr-2" />
                        Show More ({analytics.top_items.by_quantity.length - 5} more items)
                      </>
                    )}
                  </Button>
                )}
              </div>

              {/* Items Performance Stats */}
              <div>
                <h4 className="font-semibold mb-3">Item Performance</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <div className="text-sm text-orange-600 font-medium">Total Items Sold</div>
                    <div className="text-2xl font-bold text-orange-700">
                      {analytics.top_items.by_quantity.reduce((sum, item) => sum + item.quantity_sold, 0).toLocaleString()}
                    </div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm text-blue-600 font-medium">Best Seller</div>
                    <div className="text-lg font-bold text-blue-700">
                      {topItemsByQuantity[0]?.name || 'No data'}
                    </div>
                    <div className="text-sm text-blue-600">
                      {topItemsByQuantity[0]?.quantity_sold || 0} units sold
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-green-600 font-medium">Category Leader</div>
                    <div className="text-lg font-bold text-green-700">
                      {topItemsByQuantity[0]?.category || 'No category'}
                    </div>
                    <div className="text-sm text-green-600">
                      ETB {topItemsByQuantity[0]?.revenue?.toLocaleString() || 0} revenue
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Recent Activity Summary */}
        <div className="pt-4 border-t">
          <h4 className="font-semibold mb-3">Recent Activity (Last 30 Days)</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">New Members:</span>
              <div className="font-semibold">{analytics.recent_activity.new_members}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Orders:</span>
              <div className="font-semibold">{analytics.recent_activity.receipts}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Revenue:</span>
              <div className="font-semibold">ETB {analytics.recent_activity.revenue.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Transactions:</span>
              <div className="font-semibold">{analytics.recent_activity.transactions}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default BusinessPurchaseAnalytics
