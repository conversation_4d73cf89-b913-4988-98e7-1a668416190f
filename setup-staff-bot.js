// eslint-disable-next-line @typescript-eslint/no-require-imports
const fetch = require('node-fetch');

const STAFF_BOT_TOKEN = process.env.TELEGRAM_STAFF_BOT_TOKEN || '**********************************************';
const WEBHOOK_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

async function setupStaffBot() {
  try {
    console.log('Setting up Loyal ET Staff Bot...');

    // 1. Set webhook
    const webhookResponse = await fetch(`https://api.telegram.org/bot${STAFF_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: `${WEBHOOK_URL}/api/telegram/staff-webhook`,
        drop_pending_updates: true
      }),
    });

    const webhookResult = await webhookResponse.json();
    console.log('Webhook setup result:', webhookResult);

    // 2. Set bot commands
    const commandsResponse = await fetch(`https://api.telegram.org/bot${STAFF_BOT_TOKEN}/setMyCommands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        commands: [
          {
            command: 'start',
            description: 'Start the bot / Accept invitation'
          },
          {
            command: 'help',
            description: 'Show all available commands'
          },
          {
            command: 'dashboard',
            description: 'View business dashboard'
          },
          {
            command: 'invite',
            description: 'Generate staff invitation link'
          },
          {
            command: 'members',
            description: 'Manage loyalty members'
          },
          {
            command: 'settings',
            description: 'Business settings'
          }
        ]
      }),
    });

    const commandsResult = await commandsResponse.json();
    console.log('Commands setup result:', commandsResult);

    // 3. Set bot description
    const descriptionResponse = await fetch(`https://api.telegram.org/bot${STAFF_BOT_TOKEN}/setMyDescription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        description: '👥 Business management assistant for Loyal ET platform. Manage customers, process transactions, and track business performance.'
      }),
    });

    const descriptionResult = await descriptionResponse.json();
    console.log('Description setup result:', descriptionResult);

    // 4. Set bot short description
    const shortDescResponse = await fetch(`https://api.telegram.org/bot${STAFF_BOT_TOKEN}/setMyShortDescription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        short_description: 'Staff management bot for Loyal ET businesses'
      }),
    });

    const shortDescResult = await shortDescResponse.json();
    console.log('Short description setup result:', shortDescResult);

    console.log('✅ Loyal ET Staff Bot setup completed!');
    console.log(`🤖 Bot URL: https://t.me/Loyal_ET_staff_bot`);
    console.log(`🔗 Webhook: ${WEBHOOK_URL}/api/telegram/staff-webhook`);

  } catch (error) {
    console.error('❌ Error setting up staff bot:', error);
  }
}

setupStaffBot();
