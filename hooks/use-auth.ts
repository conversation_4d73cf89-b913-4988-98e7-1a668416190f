'use client'

import { useQuery, useQueryClient } from '@tanstack/react-query'
import { createClient } from '@/lib/supabase/client'
import { useEffect } from 'react'
import type { User, Session } from '@supabase/supabase-js'

interface AuthData {
  user: User | null
  session: Session | null
}

/**
 * Consolidated authentication hook using React Query
 * Optimized for performance with proper caching and minimal refetching
 * Follows official Supabase SSR patterns with efficient auth state management
 */
export function useAuth() {
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey: ['auth'],
    queryFn: async (): Promise<AuthData> => {
      console.log('🔍 Auth query function called at', new Date().toISOString())
      const supabase = createClient()

      try {
        // Use getSession first for speed, then validate with getUser only if session exists
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log('📄 Session check:', { session: !!session, error: sessionError })

        if (sessionError) {
          console.warn('Session error:', sessionError)
          return { user: null, session: null }
        }

        if (session) {
          const { data: { user }, error: userError } = await supabase.auth.getUser()
          console.log('👤 User check:', { user: !!user, email: user?.email, error: userError })

          if (userError) {
            console.warn('User validation error:', userError)
            return { user: null, session: null }
          }

          return { user, session }
        }

        console.log('❌ No session found')
        return { user: null, session: null }
      } catch (error) {
        console.error('🚨 Auth query error:', error)
        // Return null state on any error instead of throwing
        return { user: null, session: null }
      }
    },
    // Optimized caching configuration for auth data
    staleTime: 5 * 60 * 1000, // 5 minutes - auth data doesn't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache longer
    retry: 1, // Only retry once to prevent request storms
    // Use global refetch settings (false) to prevent cascading requests
    refetchOnMount: false, // Respect global config - don't refetch on every mount
    refetchOnWindowFocus: false, // Respect global config - don't refetch on focus
    refetchOnReconnect: true, // Still refetch on network reconnect for security
  })

  // Optimized auth state listener - only invalidate on meaningful changes
  useEffect(() => {
    console.log('🎧 Setting up optimized auth state listener')
    const supabase = createClient()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state changed:', {
        event,
        hasSession: !!session,
        sessionExists: !!session
      })

      // Only invalidate on meaningful auth changes to prevent unnecessary refetches
      const meaningfulEvents = ['SIGNED_IN', 'SIGNED_OUT', 'TOKEN_REFRESHED']
      if (meaningfulEvents.includes(event)) {
        console.log('🔄 Invalidating auth cache due to meaningful event:', event)
        queryClient.invalidateQueries({ queryKey: ['auth'] })
      } else {
        console.log('⏭️ Skipping cache invalidation for event:', event)
      }
    })

    return () => {
      console.log('🔌 Cleaning up auth state listener')
      subscription.unsubscribe()
    }
  }, [queryClient])

  return {
    user: query.data?.user || null,
    session: query.data?.session || null,
    isLoading: query.isLoading,
    error: query.error,
    isAuthenticated: !!(query.data?.user && query.data?.session),
  }
}

// Helper hook for auth-dependent components
export function useRequireAuth() {
  const auth = useAuth()

  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      window.location.href = '/login'
    }
  }, [auth.isLoading, auth.isAuthenticated])

  return auth
}

// Helper for logout functionality
export async function signOut() {
  const supabase = createClient()
  const { error } = await supabase.auth.signOut()

  if (error) {
    console.error('Sign out error:', error)
    throw error
  }

  // Clear any app-specific storage
  try {
    localStorage.removeItem('loyal_app_user_id')
    sessionStorage.removeItem('loyal_app_user_id')
  } catch (e) {
    console.warn('Failed to clear storage:', e)
  }

  // Redirect to login
  window.location.href = '/login'
}

// Helper for getting user ID quickly (for backwards compatibility)
export function getUserId(): string | null {
  // Try to get from window first (fastest)
  if (typeof window !== 'undefined') {
    const userId = (window as { __LOYAL_USER_ID?: string }).__LOYAL_USER_ID
    if (userId) return userId
  }

  return null
}
