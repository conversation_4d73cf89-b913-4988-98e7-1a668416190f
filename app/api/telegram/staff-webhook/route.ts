import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Interface for Telegram webhook payload
interface TelegramMessage {
  message_id: number
  from: {
    id: number
    first_name: string
    last_name?: string
    username?: string
  }
  chat: {
    id: number
    type: string
    first_name?: string
    last_name?: string
    username?: string
  }
  date: number
  text?: string
}

interface TelegramUpdate {
  update_id: number
  message?: TelegramMessage
}

// Staff bot webhook handler
export async function POST(request: NextRequest) {
  try {
    const body: TelegramUpdate = await request.json()
    console.log('Staff Bot Webhook received:', JSON.stringify(body, null, 2))

    if (!body.message) {
      return NextResponse.json({ ok: true })
    }

    const { message } = body
    const chatId = message.chat.id
    const text = message.text || ''
    const firstName = message.from.first_name
    const username = message.from.username

    console.log(`Staff Bot - Message from ${firstName} (@${username}): ${text}`)

    // Handle different commands
    if (text.startsWith('/start')) {
      await handleStaffStart(chatId, text, firstName)
    } else if (text.startsWith('/help')) {
      await handleStaffHelp(chatId)
    } else if (text.startsWith('/dashboard')) {
      await handleStaffDashboard(chatId)
    } else if (text.startsWith('/invite')) {
      await handleStaffInvite(chatId, text)
    } else {
      await sendStaffMessage(chatId, "I didn't understand that command. Type /help to see available commands.")
    }

    return NextResponse.json({ ok: true })
  } catch (error) {
    console.error('Staff Bot Webhook error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle /start command for staff bot
async function handleStaffStart(chatId: number, text: string, firstName: string) {
  const parts = text.split(' ')

  if (parts.length > 1) {
    // Handle invitation token
    const token = parts[1]
    if (token.startsWith('invite_')) {
      await handleStaffInvitation(chatId, token, firstName)
      return
    }
  }

  // Regular start message
  const welcomeMessage = `👋 Welcome to Loyal ET Staff Bot, ${firstName}!

🏢 This bot is designed for business staff members to manage operations.

Available commands:
/help - Show all commands
/dashboard - View business metrics
/invite - Generate staff invitation links
/members - Member management (coming soon)
/settings - Business settings (coming soon)

⚠️ Note: You need to be registered as staff member to use most features.`

  await sendStaffMessage(chatId, welcomeMessage)
}

// Handle staff invitation acceptance
async function handleStaffInvitation(chatId: number, token: string, firstName: string) {
  try {
    const supabase = await createClient()

    // Verify invitation token
    const { data: invitation, error } = await supabase
      .from('cashier_invitations')
      .select('*')
      .eq('token', token)
      .eq('status', 'pending')
      .single()

    if (error || !invitation) {
      await sendStaffMessage(chatId, '❌ Invalid or expired invitation link.')
      return
    }

    // Update invitation with Telegram chat ID
    const { error: updateError } = await supabase
      .from('cashier_invitations')
      .update({
        telegram_chat_id: chatId.toString(),
        status: 'accepted',
        accepted_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    if (updateError) {
      console.error('Error updating invitation:', updateError)
      await sendStaffMessage(chatId, '❌ Error processing invitation. Please try again.')
      return
    }

    await sendStaffMessage(chatId, `✅ Welcome to the team, ${firstName}!

🎯 You're now registered as staff for ${invitation.company_name}.

Available commands:
/dashboard - View business metrics
/help - Show all commands

You can now use staff features through this bot.`)

  } catch (error) {
    console.error('Error handling staff invitation:', error)
    await sendStaffMessage(chatId, '❌ Error processing invitation. Please contact your manager.')
  }
}

// Handle /help command
async function handleStaffHelp(chatId: number) {
  const helpMessage = `🤖 Loyal ET Staff Bot Commands:

👥 **Staff Management:**
/invite <role> <email> - Generate staff invitation
/dashboard - View business metrics
/members - Member management (coming soon)

⚙️ **Settings:**
/settings - Business configuration (coming soon)
/notifications - Manage alerts (coming soon)

📊 **Analytics:**
/reports - Generate reports (coming soon)
/transactions - Recent transactions (coming soon)

❓ **Support:**
/help - Show this message
/contact - Contact support (coming soon)

💡 **Tip:** Use /dashboard to get started with business overview.`

  await sendStaffMessage(chatId, helpMessage)
}

// Handle /dashboard command
async function handleStaffDashboard(chatId: number) {
  try {
    // TODO: Verify user is staff member
    // For now, show basic info
    const dashboardMessage = `📊 **Business Dashboard**

📈 **Quick Stats:**
• Total Members: Loading...
• Points Issued: Loading...
• Active Rewards: Loading...

🔄 **Recent Activity:**
• New members today: Loading...
• Transactions: Loading...

⚡ **Quick Actions:**
/members - Manage members
/invite - Invite new staff
/reports - View detailed reports

*Full dashboard integration coming soon!*`

    await sendStaffMessage(chatId, dashboardMessage)
  } catch (error) {
    console.error('Error in staff dashboard:', error)
    await sendStaffMessage(chatId, '❌ Error loading dashboard. Please try again.')
  }
}

// Handle /invite command
async function handleStaffInvite(chatId: number, text: string) {
  const parts = text.split(' ')

  if (parts.length < 3) {
    await sendStaffMessage(chatId, `📧 **Staff Invitation Generator**

Usage: /invite <role> <email>

**Available Roles:**
• cashier - Can process transactions
• manager - Can manage staff and settings

**Example:**
/<NAME_EMAIL>`)
    return
  }

  const role = parts[1].toLowerCase()
  const email = parts[2]

  if (!['cashier', 'manager'].includes(role)) {
    await sendStaffMessage(chatId, '❌ Invalid role. Use: cashier or manager')
    return
  }

  // TODO: Generate invitation link
  await sendStaffMessage(chatId, `✅ **Invitation Generated**

📧 Email: ${email}
👤 Role: ${role}

📱 Invitation link:
t.me/Loyal_ET_staff_bot?start=invite_SAMPLE_TOKEN

*Full implementation coming soon!*`)
}

// Send message using staff bot token
async function sendStaffMessage(chatId: number, text: string) {
  const token = process.env.TELEGRAM_STAFF_BOT_TOKEN

  if (!token) {
    console.error('Staff bot token not configured')
    return
  }

  try {
    const response = await fetch(`https://api.telegram.org/bot${token}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text,
        parse_mode: 'Markdown'
      }),
    })

    if (!response.ok) {
      console.error('Failed to send staff message:', await response.text())
    }
  } catch (error) {
    console.error('Error sending staff message:', error)
  }
}
