import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { createClient } from "@/lib/supabase/server";
import { getCompanyIdFromSession } from "@/lib/auth";

// Schema for member update
const memberUpdateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  email: z.string().email("Please enter a valid email").or(z.literal("")),
  phone_number: z.string().min(10, "Phone number must be at least 10 digits"),
  birthday: z.string().or(z.literal("")), // Allow empty string, handle it below
  loyalty_tier: z.string().optional().or(z.literal("")),
  notes: z.string().optional().or(z.literal("")),
  profile_image_url: z.string().optional().or(z.literal("")),
  company_id: z.string().uuid().optional(), // Add company_id as fallback
});

// GET /api/members/[id] - Get a specific member
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const memberId = (await params).id;
    const supabase = await createClient();

    // Get companyId from query param or from session
    const { searchParams } = new URL(request.url);
    const queryCompanyId = searchParams.get('companyId');

    // First try to get it from session, then fallback to query param
    let companyId = await getCompanyIdFromSession(supabase);

    // If no company ID from session but we have it in query params, use that
    if (!companyId && queryCompanyId) {
      console.log(`Using companyId from query: ${queryCompanyId}`);
      companyId = queryCompanyId;
    }

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized - No company ID available" },
        { status: 401 }
      );
    }

    // Fetch from member_points_live view for accurate calculated points data
    const { data, error } = await supabase
      .from("member_points_live")
      .select("*")
      .eq("id", memberId)
      .eq("company_id", companyId)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // If member_points_live view doesn't exist, fallback to loyalty_members table
        console.log("member_points_live view not found, falling back to loyalty_members table");

        const { data: fallbackData, error: fallbackError } = await supabase
          .from("loyalty_members")
          .select("*")
          .eq("id", memberId)
          .eq("company_id", companyId)
          .single();

        if (fallbackError) {
          if (fallbackError.code === "PGRST116") {
            return NextResponse.json(
              { error: "Member not found" },
              { status: 404 }
            );
          }
          console.error("Error fetching member from fallback:", fallbackError);
          return NextResponse.json(
            { error: "Failed to fetch member" },
            { status: 500 }
          );
        }

        // Calculate available points if not present
        const calculatedData = {
          ...fallbackData,
          available_points: (fallbackData.lifetime_points || 0) - (fallbackData.redeemed_points || 0) - (fallbackData.expired_points || 0)
        };

        return NextResponse.json(calculatedData);
      }
      console.error("Error fetching member:", error);
      return NextResponse.json(
        { error: "Failed to fetch member" },
        { status: 500 }
      );
    }

    // Fetch birthday_month_day from loyalty_members table since it's not in member_points_live view
    const { data: birthdayData, error: birthdayError } = await supabase
      .from("loyalty_members")
      .select("birthday_month_day")
      .eq("id", memberId)
      .eq("company_id", companyId)
      .single();

    // Merge the birthday data with the member data
    const memberData = {
      ...data,
      birthday_month_day: birthdayData?.birthday_month_day || null
    };

    return NextResponse.json(memberData);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/members/[id] - Update a specific member
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const memberId = (await params).id;
    const supabase = await createClient();

    const body = await request.json();
    console.log('=== Member Update API Called ===');
    console.log('Member ID:', memberId);
    console.log('Request body:', JSON.stringify(body, null, 2));

    // Validate request body
    const validationResult = memberUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      console.log('Validation failed:', JSON.stringify(validationResult.error.format(), null, 2));
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Get companyId from multiple sources in order of preference:
    // 1. Request body (explicit)
    // 2. Query parameter
    // 3. Session (fallback)
    const { searchParams } = new URL(request.url);
    const queryCompanyId = searchParams.get('companyId');

    let companyId = validationResult.data.company_id || queryCompanyId;

    if (!companyId) {
      try {
        companyId = await getCompanyIdFromSession(supabase);
      } catch (error) {
        console.log('Session company ID retrieval failed:', error);
      }
    }

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized - No company ID available" },
        { status: 401 }
      );
    }

    // Prepare update data, handling empty strings appropriately
    const updateData: Record<string, string | null> = {
      name: validationResult.data.name,
      phone_number: validationResult.data.phone_number,
    };

    // Only update email if it's provided and valid
    if (validationResult.data.email && validationResult.data.email !== "") {
      updateData.email = validationResult.data.email;
    }

    // Only update birthday if it's provided
    if (validationResult.data.birthday && validationResult.data.birthday !== "") {
      updateData.birthday = validationResult.data.birthday;
    }

    // Handle optional fields
    if (validationResult.data.loyalty_tier && validationResult.data.loyalty_tier !== "") {
      updateData.loyalty_tier = validationResult.data.loyalty_tier;
    }

    if (validationResult.data.notes !== undefined) {
      updateData.notes = validationResult.data.notes || null;
    }

    if (validationResult.data.profile_image_url !== undefined) {
      updateData.profile_image_url = validationResult.data.profile_image_url || null;
    }

    console.log('Final update data:', JSON.stringify(updateData, null, 2));

    const { data, error } = await supabase
      .from("loyalty_members")
      .update(updateData)
      .eq("id", memberId)
      .eq("company_id", companyId)
      .select()
      .single();

    if (error) {
      console.error("Error updating member:", error);
      return NextResponse.json(
        { error: "Failed to update member" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
